# 🌐 CodePins 国际化开发指南

本文档为 CodePins 插件的国际化（i18n）开发提供详细指导，帮助开发者正确实现多语言支持。

## 📋 目录

- [概述](#概述)
- [项目结构](#项目结构)
- [开发规范](#开发规范)
- [实践示例](#实践示例)
- [常见问题](#常见问题)
- [最佳实践](#最佳实践)

## 🎯 概述

CodePins 使用标准的 Java ResourceBundle 机制实现国际化，目前支持：
- 🇺🇸 **英文（English）** - 默认语言
- 🇨🇳 **中文（简体中文）** - 完整支持

### 核心组件

- **CodePinsBundle**: 主要的国际化工具类
- **LanguageSettings**: 语言设置管理
- **资源文件**: 存储各语言的文本内容

## 📁 项目结构

```
src/main/
├── java/cn/ilikexff/codepins/
│   ├── i18n/
│   │   └── CodePinsBundle.java          # 国际化工具类
│   └── settings/
│       ├── LanguageSettings.java        # 语言设置服务
│       └── LanguageSettingsConfigurable.java  # 语言设置界面
└── resources/
    └── messages/
        ├── CodePinsBundle.properties           # 默认资源文件（英文）
        ├── CodePinsBundle_en.properties       # 英文资源文件
        └── CodePinsBundle_zh_CN.properties    # 中文资源文件
```

## 📝 开发规范

### 1. 添加新的文本内容

当需要添加新的用户界面文本时，请遵循以下步骤：

#### 步骤 1: 定义资源键
在资源文件中添加新的键值对，使用有意义的键名：

```properties
# 好的键名示例
button.save=Save
dialog.confirm.delete=Are you sure you want to delete this item?
notification.success.title=Success

# 避免的键名示例
btn1=Save
msg=Are you sure?
title=Success
```

#### 步骤 2: 添加到所有语言文件
确保在所有支持的语言文件中添加相同的键：

**CodePinsBundle.properties (英文)**:
```properties
feature.new.title=New Feature
feature.new.description=This is a new feature description
```

**CodePinsBundle_zh_CN.properties (中文)**:
```properties
feature.new.title=新功能
feature.new.description=这是一个新功能的描述
```

#### 步骤 3: 在代码中使用
使用 `CodePinsBundle.message()` 方法获取国际化文本：

```java
// 简单文本
String title = CodePinsBundle.message("feature.new.title");

// 带参数的文本
String message = CodePinsBundle.message("notification.items.deleted", count);
```

### 2. 键名命名规范

使用层次化的键名结构，便于管理和查找：

```properties
# UI 组件分类
button.add=Add
button.edit=Edit
button.delete=Delete

# 对话框分类
dialog.confirm.title=Confirmation
dialog.confirm.delete=Are you sure you want to delete?
dialog.error.title=Error

# 通知分类
notification.success.title=Success
notification.error.title=Error
notification.warning.title=Warning

# 设置分类
settings.general.title=General Settings
settings.language.title=Language Settings
```

### 3. 参数化文本

对于包含动态内容的文本，使用 MessageFormat 参数：

```properties
# 资源文件
notification.items.selected=Selected {0} items
file.size.info=File size: {0} KB, modified: {1}
```

```java
// Java 代码
String message = CodePinsBundle.message("notification.items.selected", count);
String info = CodePinsBundle.message("file.size.info", fileSize, modifiedDate);
```

## 💡 实践示例

### 示例 1: 添加新的对话框

假设要添加一个新的确认对话框：

**1. 添加资源键**:
```properties
# CodePinsBundle.properties
dialog.export.title=Export Pins
dialog.export.message=Choose export format and location
dialog.export.format.label=Export Format:
dialog.export.location.label=Save Location:

# CodePinsBundle_zh_CN.properties  
dialog.export.title=导出图钉
dialog.export.message=选择导出格式和位置
dialog.export.format.label=导出格式：
dialog.export.location.label=保存位置：
```

**2. 在代码中使用**:
```java
public class ExportDialog extends JDialog {
    public ExportDialog() {
        setTitle(CodePinsBundle.message("dialog.export.title"));
        
        JLabel messageLabel = new JLabel(CodePinsBundle.message("dialog.export.message"));
        JLabel formatLabel = new JLabel(CodePinsBundle.message("dialog.export.format.label"));
        JLabel locationLabel = new JLabel(CodePinsBundle.message("dialog.export.location.label"));
    }
}
```

### 示例 2: 添加带参数的通知

**1. 添加资源键**:
```properties
# CodePinsBundle.properties
notification.export.success=Successfully exported {0} pins to {1}
notification.export.failed=Failed to export pins: {0}

# CodePinsBundle_zh_CN.properties
notification.export.success=成功导出 {0} 个图钉到 {1}
notification.export.failed=导出图钉失败：{0}
```

**2. 在代码中使用**:
```java
// 成功通知
String successMessage = CodePinsBundle.message("notification.export.success", pinCount, filePath);
Notifications.Bus.notify(new Notification("CodePins", "Export", successMessage, NotificationType.INFORMATION));

// 失败通知  
String errorMessage = CodePinsBundle.message("notification.export.failed", exception.getMessage());
Notifications.Bus.notify(new Notification("CodePins", "Export", errorMessage, NotificationType.ERROR));
```

## ❓ 常见问题

### Q1: 如何处理复数形式？
A: 在英文中使用条件判断，中文通常不需要复数形式：

```properties
# 英文
notification.pins.count={0,choice,0#No pins|1#1 pin|1<{0} pins}

# 中文  
notification.pins.count={0} 个图钉
```

### Q2: 如何处理长文本？
A: 使用 `\n` 进行换行，或者使用 HTML 格式：

```properties
# 普通换行
help.text=Line 1\nLine 2\nLine 3

# HTML 格式
help.html=<html>Line 1<br>Line 2<br>Line 3</html>
```

### Q3: 如何添加新语言支持？
A: 创建新的资源文件并更新语言设置：

1. 创建 `CodePinsBundle_fr.properties`（法语示例）
2. 在 `LanguageSettings.Language` 枚举中添加新语言
3. 更新语言选择界面

## ✨ 最佳实践

### 1. 文本编写原则
- **简洁明了**: 使用简短、清晰的文本
- **用户友好**: 使用用户容易理解的术语
- **一致性**: 保持相同概念在不同地方使用相同的词汇

### 2. 键名管理
- **分组管理**: 使用前缀对相关键进行分组
- **描述性**: 键名应该能够描述其用途
- **避免重复**: 相同的文本使用相同的键

### 3. 开发流程
1. **设计阶段**: 提前规划需要国际化的文本
2. **开发阶段**: 立即添加到资源文件，不要硬编码
3. **测试阶段**: 在不同语言环境下测试界面
4. **维护阶段**: 定期检查和更新翻译

### 4. 代码审查要点
- ✅ 检查是否有硬编码的用户界面文本
- ✅ 确认新增的键在所有语言文件中都存在
- ✅ 验证参数化文本的参数顺序和类型
- ✅ 测试语言切换功能是否正常

## 🔧 工具和命令

### 检查缺失的翻译
```bash
# 比较英文和中文资源文件的差异
diff <(sort CodePinsBundle.properties | cut -d'=' -f1) <(sort CodePinsBundle_zh_CN.properties | cut -d'=' -f1)
```

### 验证资源文件格式
```bash
# 检查资源文件的编码和格式
file -bi src/main/resources/messages/*.properties
```

## 📞 获取帮助

如果在国际化开发过程中遇到问题：

1. **查看现有代码**: 参考项目中已有的国际化实现
2. **提交 Issue**: 在 GitHub 上提交问题
3. **参与讨论**: 在项目讨论区交流经验

---

**记住**: 良好的国际化不仅仅是翻译文本，更是为全球用户提供一致、友好的用户体验！🌍
