<h2>Getting Started with CodePins</h2>

<p>Welcome to CodePins! Follow these simple steps to enhance your coding workflow:</p>

<h3>1. Adding Pins</h3>
<ul>
    <li><strong>Right-click method:</strong> Right-click on any code line and select "Pin This Line"</li>
    <li><strong>Keyboard shortcut:</strong> Place cursor on any line and press <code>Alt+Shift+P</code></li>
    <li>Add an optional note to remember why this code is important</li>
</ul>

<h3>2. Viewing & Navigating</h3>
<ul>
    <li>Open the CodePins tool window from the left sidebar or press <code>Alt+Shift+T</code></li>
    <li>Double-click any pin to jump to its location</li>
    <li>Use <code>Alt+Shift+RIGHT</code> and <code>Alt+Shift+LEFT</code> to navigate between pins</li>
</ul>

<h3>3. Managing Pins</h3>
<ul>
    <li>Search pins by typing in the search box (filters by file path or note content)</li>
    <li>Drag and drop pins to reorder them</li>
    <li>Right-click on pins for additional options (edit note, delete, view code)</li>
    <li>Use tags in notes (e.g., #important, #bug) for better organization</li>
</ul>

<h3>4. Advanced Features</h3>
<ul>
    <li><strong>Code blocks:</strong> Select multiple lines before adding a pin</li>
    <li><strong>Import/Export:</strong> Share pins between projects or team members</li>
    <li><strong>Persistent storage:</strong> Your pins are saved automatically between IDE sessions</li>
</ul>

<p>For more detailed information, visit our <a href="https://github.com/08820048/codepins-intellij-plugin">GitHub repository</a>.</p>
