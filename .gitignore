.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store
._*

### Security - 敏感信息 ###
# 密钥文件
*.key
*.pem
*.p12
*.keystore
*.jks

# 敏感配置文件
secrets.properties
local.properties
.env
.env.local
.env.production
config/secrets/

# 许可证和证书
license.dat
*.license
*.cert

# API 密钥和令牌
api-keys.txt
tokens.txt
credentials.json

# 临时敏感文件
temp-secrets/
.secrets/
*.tmp
tmp/

# 日志文件
*.log

# 个人测试文件
*Test.class
