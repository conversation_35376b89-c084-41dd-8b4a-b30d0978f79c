# 📚 CodePins 详细文档

## 目录

- [功能详解](#功能详解)
  - [图钉管理](#图钉管理)
  - [标签系统](#标签系统)
  - [注释标记](#注释标记)
  - [导入导出](#导入导出)
- [使用教程](#使用教程)
  - [添加图钉](#添加图钉)
  - [管理图钉](#管理图钉)
  - [使用标签](#使用标签)
  - [注释标记语法](#注释标记语法)
- [高级功能](#高级功能)
  - [快捷键](#快捷键)
  - [设置选项](#设置选项)
  - [团队协作](#团队协作)
- [常见问题](#常见问题)

## 功能详解

### 图钉管理

CodePins 提供了强大的图钉管理功能，让您可以轻松地组织和导航代码中的重要位置。

#### 图钉类型

- **单行图钉**：标记单行代码
- **代码块图钉**：标记一段代码块
- **自定义范围图钉**：指定特定行号范围的代码块

#### 图钉属性

每个图钉都可以包含以下属性：
- 备注：详细说明图钉的用途或注意事项
- 标签：用于分类和筛选图钉
- 创建时间：自动记录图钉的创建时间
- 位置信息：包括文件路径、行号等

### 标签系统

标签系统是 CodePins 的核心功能之一，它允许您使用标签来组织和分类图钉，提高工作效率。

#### 标签功能

- **自定义标签**：创建任意数量的自定义标签
- **标签筛选**：根据标签快速筛选图钉
- **标签管理**：编辑、删除和重命名标签
- **预定义标签**：设置常用标签，快速应用

#### 标签最佳实践

- 使用描述性标签，如 #bug, #todo, #important
- 为不同项目或模块创建特定标签
- 使用标签组合进行精确筛选

### 注释标记

CodePins 支持通过代码注释自动创建图钉，这是一种非常便捷的方式，特别适合团队协作。

#### 注释标记语法

- **单行图钉**：`@cp 备注内容 #标签名`（也兼容旧格式 `@pin`）
- **代码块图钉**：`@cpb 备注内容 #标签名`（也兼容旧格式 `@pin-block`）
- **指定行号范围**：`@cpb1-20: 备注内容 #标签名`（创建从第1行到第20行的代码块图钉）

#### 示例

```java
// @cp 这是一个重要函数 #重要 #待办
public void importantFunction() { ... }

// @cpb 这个类需要重构 #重构 #技术债
public class LegacyClass { ... }

// @cpb1-20 这段代码有性能问题 #性能 #优化
// 20行需要优化的代码
```

### 导入导出

CodePins 提供了导入导出功能，方便在不同项目间共享图钉或与团队成员协作。

#### 导出格式

- **JSON 格式**：包含完整的图钉信息，包括备注和标签
- **简单文本**：仅包含基本的位置信息和备注

#### 使用场景

- **团队协作**：分享重要代码位置给团队成员
- **知识传递**：为新团队成员提供代码导航指南
- **项目迁移**：在不同环境间迁移图钉

## 使用教程

### 添加图钉

有多种方式可以添加图钉：

1. **右键菜单**：右键点击代码行 → `Add CodePin Here`
2. **快捷键**：使用快捷键 `Alt+Shift+P` 添加图钉
3. **选择文本**：选中代码后，点击出现的浮动操作按钮添加为图钉
4. **注释标记**：在代码中添加特定格式的注释自动创建图钉

### 管理图钉

在 CodePins 工具窗口中，您可以：

1. **编辑图钉**：双击图钉编辑备注和标签
2. **删除图钉**：右键图钉 → `Delete` 或批量清空
3. **重新排序**：拖拽图钉调整顺序
4. **快速跳转**：双击图钉跳转到对应代码行

### 使用标签

标签是组织图钉的有效方式：

1. **添加标签**：在创建或编辑图钉时添加标签
2. **筛选标签**：在 CodePins 工具窗口中点击标签进行筛选
3. **管理标签**：在设置中管理全局标签

### 注释标记语法

使用注释标记自动创建图钉：

1. **添加注释**：在代码中添加符合格式的注释
2. **触发检测**：保存文件或手动触发检测（Tools > CodePins > Detect Comment Markers）
3. **确认创建**：根据设置，可能需要确认或直接创建图钉

## 高级功能

### 快捷键

CodePins 提供了多种快捷键，提高使用效率：

- `Alt+Shift+P`：添加图钉
- `Alt+Shift+Left/Right`：在图钉间导航
- `Alt+Shift+D`：删除当前位置的图钉
- `Alt+Shift+F`：打开 CodePins 工具窗口

### 设置选项

在 IDE 设置中，您可以自定义 CodePins 的行为：

1. **外观设置**：调整图钉显示样式和颜色
2. **行为设置**：配置自动保存、确认对话框等行为
3. **注释标记设置**：自定义注释标记的检测和处理方式

### 团队协作

CodePins 支持团队协作功能：

1. **共享图钉**：导出图钉分享给团队成员
2. **注释标记**：通过代码注释创建图钉，自动包含在代码库中
3. **标准化标签**：团队统一标签命名规范，提高协作效率

## 常见问题

### 图钉不显示？

- 检查图钉是否被筛选器过滤
- 确认文件路径是否正确
- 重新启动 IDE 尝试解决

### 注释标记不工作？

- 确认注释格式是否正确
- 检查是否启用了注释标记功能
- 尝试手动触发检测（Tools > CodePins > Detect Comment Markers）

### 如何批量管理图钉？

- 使用标签筛选后进行批量操作
- 导出图钉进行编辑后再导入
- 使用搜索功能找到特定图钉进行管理

### 性能问题？

- 减少单个项目中的图钉数量
- 使用更具体的标签组织图钉
- 定期清理不再需要的图钉
