![Header](./src/main/resources/META-INF/github-header-image.png)

<div align="center">

<p align="center">
   <img src="https://readme-typing-svg.herokuapp.com?font=Fira+Code&weight=600&size=24&pause=1000&color=4A7FD1&center=true&vCenter=true&random=false&width=600&height=27&lines=Pin+your+code%2C+boost+your+flow;Bookmark+smarter%2C+code+faster;Never+lose+your+place+in+code;%E7%8E%B0%E4%BB%A3%E5%8C%96%E7%9A%84%E4%BB%A3%E7%A0%81%E4%B9%A6%E7%AD%BE%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88;%E8%AE%A9%E6%82%A8%E7%9A%84%E5%BC%80%E5%8F%91%E5%B7%A5%E4%BD%9C%E6%B5%81%E6%9B%B4%E9%AB%98%E6%95%88" alt="Typing SVG">
</p>



[![JetBrains Plugin](https://img.shields.io/jetbrains/plugin/v/27300-codepins--code-bookmarks.svg)](https://plugins.jetbrains.com/plugin/27300-codepins--code-bookmarks)
[![Downloads](https://img.shields.io/jetbrains/plugin/d/27300-codepins--code-bookmarks.svg)](https://plugins.jetbrains.com/plugin/27300-codepins--code-bookmarks)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
![Java](https://img.shields.io/badge/Java-17%2B-orange)
![IntelliJ Platform](https://img.shields.io/badge/IntelliJ-Platform_SDK-blue)
![Swing](https://img.shields.io/badge/UI-Swing-brightgreen)
![PSI](https://img.shields.io/badge/Code_Analysis-PSI-purple)

</div>

## 📑 文档导航

<div align="center">

[![在线文档](https://img.shields.io/badge/官方文档-docs.codepins.cn-blue?style=for-the-badge)](https://docs.codepins.cn/)

</div>

访问我们的[官方文档网站](https://docs.codepins.cn/)获取完整的使用指南、教程和参考文档。

快速链接：

- [安装指南](#安装)
- [快速开始](#快速开始)
- [核心功能](#核心功能)
- [离线文档](DOCUMENTATION.md)
- [更新日志](CHANGELOG.md)
- [贡献指南](CONTRIBUTING.md)
- [问题反馈](https://docs.codepins.cn/bug-report)

## 🎉 完全免费开源！

**CodePins 现在完全免费开源！** 所有功能对所有用户开放，无任何限制。如果这个插件对您有帮助，请考虑：

- ⭐ **给项目点 Star**：[GitHub 仓库](https://github.com/08820048/codepins)
- ☕ **请我们喝咖啡**：[捐赠支持](https://docs.codepins.cn/donate)
- 🤝 **参与贡献**：查看 [贡献指南](CONTRIBUTING.md)

## ✨ 核心功能

- 🔖 **无限制的智能书签**：无限图钉数量，标签系统，智能备注
- 🎯 **高效导航**：快捷键导航，即时预览，智能搜索
- 🎨 **现代化界面**：美观设计，拖拽排序，卡片式布局
- 📝 **代码注释集成**：通过注释标记添加图钉，支持标签和行号范围
- 🏷️ **标签系统**：使用标签组织和筛选图钉，提高工作效率
- 🔄 **数据管理**：自动同步，导入导出，持久化存储
- 🌐 **团队协作**：导入/导出功能，便于团队共享

## 🚀 快速开始

### 添加图钉
- 右键代码行 → `Add CodePin Here`
- 使用快捷键 `Alt+Shift+P`
- 选中代码后点击浮动按钮
- 添加注释标记（如 `@cp 备注 #标签`）

### 管理图钉
- 双击图钉跳转到代码
- 右键图钉进行操作
- 使用标签组织和筛选
- 导入/导出分享给团队

更多详细操作请参考[详细文档](DOCUMENTATION.md)

## 📷 功能预览

> 面板 UI | 添加图钉 | 搜索图钉

（图片将在插件发布时更新）

## 📍 安装

### 从 JetBrains Marketplace 安装
1. 在 IDE 中，前往 `Settings/Preferences` → `Plugins` → `Marketplace`
2. 搜索 "CodePins"
3. 点击 `Install`

### 手动安装
1. 从 [JetBrains Marketplace](https://plugins.jetbrains.com/plugin/27300-codepins--code-bookmarks) 下载 `.zip` 文件
2. 在 IDE 中，前往 `Settings/Preferences` → `Plugins` → `⚙️` → `Install Plugin from Disk...`
3. 选择下载的 `.zip` 文件

更多使用技巧和详细指南请参考[详细文档](DOCUMENTATION.md)

## ✨ 贡献者

<!-- ALL-CONTRIBUTORS-BADGE:START - Do not remove or modify this section -->
[![All Contributors](https://img.shields.io/badge/all_contributors-2-orange.svg?style=flat-square)](#contributors-)
<!-- ALL-CONTRIBUTORS-BADGE:END -->

感谢这些优秀的人 ([emoji key](https://allcontributors.org/docs/en/emoji-key)):

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/08820048"><img src="https://avatars.githubusercontent.com/u/08820048" width="100px;" alt="08820048"/><br /><sub><b>08820048</b></sub></a><br /><a href="#code-08820048" title="Code">💻</a> <a href="#doc-08820048" title="Documentation">📖</a> <a href="#maintenance-08820048" title="Maintenance">🚧</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Auroral0810"><img src="https://avatars.githubusercontent.com/u/140379943?s=400&u=ea4e758ddc17a3df7a6b29bc4dc435ba1a35e999&v=4" width="100px;" alt="Auroral0810"/><br /><sub><b>Auroral0810</b></sub></a><br /><a href="#code-Auroral0810" title="Code">💻</a></td>
    </tr>
  </tbody>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->

该项目遵循 [all-contributors](https://github.com/all-contributors/all-contributors) 规范。欢迎任何形式的贡献！

## 🤝 贡献指南

我们欢迎各种形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

### 贡献方式
- 🐛 **报告 Bug**：[GitHub Issues](https://github.com/08820048/codepins/issues)
- 💡 **提出功能建议**：创建 Feature Request
- 📝 **改进文档**：完善使用指南和 API 文档
- 💻 **贡献代码**：修复 Bug 或实现新功能
- 🌍 **帮助翻译**：翻译到更多语言

## 📞 联系我们

如果您有任何问题或建议：

- 📧 **邮箱**：<EMAIL>
- 🐛 **问题反馈**：[GitHub Issues](https://github.com/08820048/codepins/issues)
- 💬 **讨论交流**：[GitHub Discussions](https://github.com/08820048/codepins/discussions)
- 🏪 **插件页面**：[JetBrains Marketplace](https://plugins.jetbrains.com/plugin/27300-codepins--code-bookmarks)
- 👥 **QQ 交流群**：扫描下方二维码加入我们的 QQ 交流群

<div align="center">
<img src="./src/main/resources/icons/qq.jpg" width="200" alt="CodePins QQ 交流群">
</div>

## 📝 更新日志

查看完整的更新日志，请参考 [CHANGELOG.md](CHANGELOG.md)。
