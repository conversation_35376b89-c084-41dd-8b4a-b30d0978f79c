package cn.ilikexff.codepins.utils;

import cn.ilikexff.codepins.services.LicenseService;
import com.intellij.openapi.project.Project;

import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;

/**
 * 水印管理工具类
 */
public class WatermarkManager {

    /**
     * 水印位置枚举
     */
    public enum WatermarkPosition {
        TOP_LEFT("左上角"),
        TOP_RIGHT("右上角"),
        BOTTOM_LEFT("左下角"),
        BOTTOM_RIGHT("右下角"),
        CENTER("中心");

        private final String displayName;

        WatermarkPosition(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 水印类型枚举
     */
    public enum WatermarkType {
        TEXT("文本"),
        IMAGE("图片"),
        NONE("无");

        private final String displayName;

        WatermarkType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 默认水印文本
    private static final String DEFAULT_WATERMARK_TEXT = "Generated by CodePins - Code Bookmarks";

    // 默认水印透明度
    private static final float DEFAULT_OPACITY = 0.5f;

    /**
     * 添加文本水印
     *
     * @param image 原始图片
     * @param text 水印文本
     * @param position 水印位置
     * @param color 水印颜色
     * @param opacity 水印透明度
     * @param isPremium 是否为付费用户
     * @return 添加水印后的图片
     */
    public static BufferedImage addTextWatermark(BufferedImage image, String text,
                                                WatermarkPosition position, Color color,
                                                float opacity, boolean isPremium) {
        // 如果不是付费用户，使用默认水印
        if (!isPremium) {
            text = DEFAULT_WATERMARK_TEXT;
            position = WatermarkPosition.BOTTOM_RIGHT;
            color = new Color(color.getRed(), color.getGreen(), color.getBlue(), 50);
            opacity = DEFAULT_OPACITY;
        }

        // 创建图片副本
        BufferedImage watermarked = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = watermarked.createGraphics();

        // 绘制原始图片
        g2d.drawImage(image, 0, 0, null);

        // 设置水印字体
        g2d.setFont(new Font("SansSerif", Font.BOLD, 12));

        // 设置水印颜色和透明度
        g2d.setColor(color);
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, opacity));

        // 计算水印位置
        int textWidth = g2d.getFontMetrics().stringWidth(text);
        int textHeight = g2d.getFontMetrics().getHeight();
        int x = 0;
        int y = 0;

        switch (position) {
            case TOP_LEFT:
                x = 10;
                y = textHeight + 10;
                break;
            case TOP_RIGHT:
                x = image.getWidth() - textWidth - 10;
                y = textHeight + 10;
                break;
            case BOTTOM_LEFT:
                x = 10;
                y = image.getHeight() - 10;
                break;
            case BOTTOM_RIGHT:
                x = image.getWidth() - textWidth - 10;
                y = image.getHeight() - 10;
                break;
            case CENTER:
                x = (image.getWidth() - textWidth) / 2;
                y = (image.getHeight() + textHeight) / 2;
                break;
        }

        // 绘制水印
        g2d.drawString(text, x, y);

        // 释放资源
        g2d.dispose();

        return watermarked;
    }

    /**
     * 添加图片水印
     *
     * @param image 原始图片
     * @param watermarkImage 水印图片
     * @param position 水印位置
     * @param opacity 水印透明度
     * @param isPremium 是否为付费用户
     * @return 添加水印后的图片
     */
    public static BufferedImage addImageWatermark(BufferedImage image, BufferedImage watermarkImage,
                                                 WatermarkPosition position, float opacity,
                                                 boolean isPremium) {
        // 图片水印功能已移除，始终使用文本水印
        return addTextWatermark(image, DEFAULT_WATERMARK_TEXT, WatermarkPosition.BOTTOM_RIGHT,
                               new Color(128, 128, 128, 50), DEFAULT_OPACITY, false);
    }

    /**
     * 移除水印
     *
     * @param project 当前项目
     * @return 是否允许移除水印
     */
    public static boolean removeWatermark(Project project) {
        // 检查是否为付费用户
        boolean isPremium = LicenseService.getInstance().isPremiumUser();

        // 如果不是付费用户，显示升级对话框并返回false
        if (!isPremium) {
            LicenseService.getInstance().showUpgradeDialogIfNeeded(project, "移除水印");
            return false;
        }

        // 付费用户可以移除水印
        return true;
    }
}
