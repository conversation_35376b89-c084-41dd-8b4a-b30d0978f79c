package cn.ilikexff.codepins.startup;

import cn.ilikexff.codepins.i18n.CodePinsBundle;
import cn.ilikexff.codepins.settings.LanguageSettings;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import org.jetbrains.annotations.NotNull;

/**
 * CodePins 启动活动
 * 在插件启动时初始化语言设置
 */
public class CodePinsStartupActivity implements StartupActivity {
    @Override
    public void runActivity(@NotNull Project project) {
        // 获取当前语言设置
        LanguageSettings settings = ApplicationManager.getApplication().getService(LanguageSettings.class);
        if (settings != null) {
            // 设置 CodePinsBundle 的语言
            CodePinsBundle.setLocale(settings.getSelectedLocale());
            System.out.println("[CodePins] 启动时设置语言: " + settings.getSelectedLocale());
        }
    }
}
