<?xml version="1.0" encoding="UTF-8"?>
<idea-plugin>
    <id>cn.ilikexff.codepins</id>
    <name resource-bundle="messages" key="plugin.name" />
    <vendor email="<EMAIL>" url="https://github.com/08820048/codepins">ilikexff</vendor>
    <version>2.0.0</version>

    <!-- 插件现在完全免费开源，移除产品描述符 -->

    <!-- 插件兼容性信息 -->
    <idea-version since-build="241" until-build="252.*"/>

    <!-- 插件分类标签 -->
    <category>Navigation</category>
    <category>Code Tools</category>

    <!-- 插件描述 -->
    <!-- 注意：Marketplace要求插件描述必须使用拉丁字符（英文），并且长度至少为40个字符 -->
    <description><![CDATA[
        <h2>CodePins - The Modern Code Bookmarking Solution</h2>

        <p>CodePins is a sophisticated code bookmarking tool designed to enhance your coding workflow in complex projects. It allows you to pin important code locations, add notes, and quickly navigate between them with elegant UI and powerful features.</p>

        <h3>Why Choose CodePins?</h3>

        <p>Unlike traditional bookmarks, CodePins offers a modern, intuitive experience with rich features like code previews, custom tags, drag-and-drop organization, and keyboard shortcuts - all designed to help you maintain focus and productivity.</p>

        <h3>Key Features</h3>

        <ul>
            <li><strong>Smart Bookmarking:</strong> Pin any line or code block with a single click or keyboard shortcut</li>
            <li><strong>Rich Annotations:</strong> Add custom notes and tags to each pin for better organization</li>
            <li><strong>Instant Preview:</strong> Hover over pins to preview code without switching context</li>
            <li><strong>Keyboard Navigation:</strong> Navigate between pins using intuitive keyboard shortcuts</li>
            <li><strong>Customizable Organization:</strong> Drag and drop pins to reorder them according to your workflow</li>
            <li><strong>Powerful Search:</strong> Quickly find pins by filename, path, note content, or tags</li>
            <li><strong>Adaptive UI:</strong> Beautiful interface that adapts to both light and dark themes</li>
            <li><strong>Auto-Update:</strong> Pin positions automatically update as you edit code</li>
            <li><strong>Import/Export:</strong> Share pins between projects or team members</li>
            <li><strong>Persistent Storage:</strong> Pin information is preserved after IDE restart</li>
        </ul>

        <h3>Perfect For</h3>

        <ul>
            <li>Navigating large codebases and complex projects</li>
            <li>Onboarding new team members by highlighting important code sections</li>
            <li>Tracking TODOs and points of interest during code reviews</li>
            <li>Creating a personal map of important code locations</li>
            <li>Maintaining context when working across multiple files</li>
        </ul>

        <p>CodePins seamlessly integrates with your development workflow, helping you stay organized and focused on what matters most - writing great code.</p>

        <h3>Free & Open Source</h3>
        <p>CodePins is now completely free and open source! No limitations, no premium features - everything is available to all users. If you find this plugin helpful, please consider supporting the development by starring our GitHub repository or making a donation.</p>

        <h3>Support Development</h3>
        <p>If CodePins helps improve your coding workflow, you can support continued development through:</p>
        <ul>
            <li>⭐ Star our GitHub repository</li>
            <li>☕ Buy us a coffee via donation</li>
            <li>🐛 Report bugs and suggest features</li>
            <li>📢 Share with fellow developers</li>
        </ul>
    ]]></description>

    <!-- 详细描述已包含在 description 标签中 -->
    <!-- 注意：pluginDescription 不是标准标签，已移除 -->

    <!-- Plugin Change Notes -->
    <change-notes><![CDATA[
<h2>Version 2.1.0</h2>
<ul>
    <li><b>📝 Comment Markers:</b> Added support for creating pins from comment markers</li>
    <li><b>✏️ Simplified Commands:</b> New shorter commands @cp and @cpb for creating pins</li>
    <li><b>📊 Line Range:</b> Support for specifying line ranges with @cpb1-20 format</li>
    <li><b>✨ Smart Triggers:</b> Pins can be created by typing a completion symbol or via menu</li>
    <li><b>🛠️ Settings:</b> Added customization options for comment pin creation</li>
    <li><b>🎨 UI Integration:</b> Added menu item in Tools menu for detecting comment markers</li>
    <li><b>🔧 Bug Fixes:</b> Fixed issues with automatic pin creation and improved stability</li>
</ul>

<h2>Version 2.0.0</h2>
<ul>
    <li><b>🎉 FREE & OPEN SOURCE:</b> CodePins is now completely free! All features unlocked for everyone</li>
    <li><b>🚫 No More Limitations:</b> Removed all pin count and tag restrictions</li>
    <li><b>💝 Donation Support:</b> Added donation links to support continued development</li>
    <li><b>🧹 Code Cleanup:</b> Removed all premium/freemium related code and UI elements</li>
    <li><b>⚡ Performance:</b> Simplified codebase for better performance and maintainability</li>
    <li><b>🎨 UI Improvements:</b> Updated settings panel with donation and GitHub links</li>
    <li><b>📝 Documentation:</b> Updated plugin description to reflect open source nature</li>
    <li><b>🔧 Simplified:</b> Removed complex license verification system</li>
</ul>

<h2>Version 1.1.2</h2>
<ul>
    <li><b>Export/Import:</b> Fixed functionality with improved UI</li>
    <li><b>UI Optimization:</b> Better support for light themes</li>
    <li><b>Drag and Drop:</b> Added manual pin reordering</li>
    <li><b>Keyboard Shortcuts:</b> Alt+Shift+P to add pin, Alt+Shift+Left/Right to navigate</li>
    <li><b>Search Box:</b> Modern design with dark theme support</li>
    <li><b>Code Preview:</b> Enhanced cards with sophisticated styling</li>
    <li><b>Animations:</b> Added effects when hovering over list items</li>
    <li><b>Icons:</b> Updated for import/export functionality</li>
</ul>

<h2>Version 1.1.1</h2>
<ul>
    <li><b>Hover Preview:</b> Improved functionality, resolved thread safety issues</li>
    <li><b>Code Preview UI:</b> Dynamic height adjustment based on code length</li>
    <li><b>Pin List:</b> Completely redesigned with modern card-style</li>
    <li><b>Empty State:</b> Added view with friendly guidance</li>
    <li><b>Search Field:</b> Enhanced visual feedback and user experience</li>
    <li><b>Tag System:</b> Better organization and filtering of pins</li>
</ul>

<h2>Version 1.0.0</h2>
<ul>
    <li><b>Initial Release:</b> Core functionality for code bookmarking</li>
    <li><b>Pin Management:</b> Add, delete, search, and manage code pins</li>
    <li><b>Code Marking:</b> Support for blocks and single line marking</li>
    <li><b>Modern UI:</b> Clean design and optimized user experience</li>
    <li><b>Notes:</b> Support for pin notes and quick preview</li>
</ul>
    ]]></change-notes>

    <depends>com.intellij.modules.platform</depends>

    <extensions defaultExtensionNs="com.intellij">
        <toolWindow id="CodePins"
                    anchor="left"
                    factoryClass="cn.ilikexff.codepins.PinsToolWindow"
                    icon="/icons/logo_13x13.svg"
        />

        <!-- 注册服务类 -->
        <applicationService serviceImplementation="cn.ilikexff.codepins.core.PinStateService"/>
        <applicationService serviceImplementation="cn.ilikexff.codepins.settings.CodePinsSettings"/>
        <applicationService serviceImplementation="cn.ilikexff.codepins.settings.LanguageSettings"/>
        <applicationService serviceImplementation="cn.ilikexff.codepins.services.GistService"/>
        <applicationService serviceImplementation="cn.ilikexff.codepins.services.LicenseService"/>
        <applicationService serviceImplementation="cn.ilikexff.codepins.services.IconPreloader"/>

        <!-- 图标预加载器 -->
        <postStartupActivity implementation="cn.ilikexff.codepins.services.IconPreloader"/>
        
        <!-- 选择文本后的浮动操作按钮初始化器 -->
        <postStartupActivity implementation="cn.ilikexff.codepins.extensions.PinSelectionPopupInitializer"/>
        
        <!-- 注释：移除了注释标记检测器初始化器，避免 PSI 树变化时自动触发图钉添加 -->
        
        <!-- 注释：移除了文档监听器初始化器，避免文档变化时自动触发图钉添加 -->
        
        <!-- 注释：移除了文件保存时自动检测注释标记的功能，只保留手动触发和完成符号触发 -->
        
        <!-- 完成符号监听器初始化器，用于检测完成符号的输入 -->
        <postStartupActivity implementation="cn.ilikexff.codepins.extensions.PinCompletionSymbolListenerInitializer"/>
        
        <!-- 语言设置初始化器，用于在插件启动时设置正确的语言 -->
        <postStartupActivity implementation="cn.ilikexff.codepins.startup.CodePinsStartupActivity"/>


        <!-- 注册设置页面 -->
        <applicationConfigurable parentId="tools" instance="cn.ilikexff.codepins.settings.CodePinsSettingsConfigurable"
                             id="cn.ilikexff.codepins.settings.CodePinsSettingsConfigurable"
                             displayName="CodePins Settings"/>
                             
        <!-- 注册语言设置页面 -->
        <applicationConfigurable parentId="cn.ilikexff.codepins.settings.CodePinsSettingsConfigurable" 
                             instance="cn.ilikexff.codepins.settings.LanguageSettingsConfigurable"
                             id="cn.ilikexff.codepins.settings.LanguageSettingsConfigurable"
                             displayName="Language"/>
    </extensions>

    <actions>
        <!-- Right-click menu add pin -->
        <action id="CodePins.PinAction"
                class="cn.ilikexff.codepins.PinAction"
                text="Add CodePin Here"
                description="Pin current line for later reference"
                icon="/icons/pin-here.svg">
            <add-to-group group-id="EditorPopupMenu" anchor="last"/>
        </action>

        <!-- Shortcut key add pin -->
        <action id="CodePins.AddPinAction"
                class="cn.ilikexff.codepins.actions.AddPinAction"
                text="Add Pin at Cursor"
                description="Add a pin at the current cursor position"
                icon="/icons/pin-add.svg">
            <keyboard-shortcut keymap="$default" first-keystroke="alt shift P"/>
            <add-to-group group-id="EditorActions" anchor="last"/>
        </action>

        <!-- Navigate to next pin -->
        <action id="CodePins.NavigateNextPinAction"
                class="cn.ilikexff.codepins.actions.NavigateNextPinAction"
                text="action.navigate.next"
                description="action.navigate.next.description"
                resource-bundle="messages.CodePinsBundle"
                icon="/icons/pin-next.svg">
            <keyboard-shortcut keymap="$default" first-keystroke="alt shift RIGHT"/>
            <add-to-group group-id="EditorActions" anchor="last"/>
        </action>

        <!-- Navigate to previous pin -->
        <action id="CodePins.NavigatePrevPinAction"
                class="cn.ilikexff.codepins.actions.NavigatePrevPinAction"
                text="action.navigate.prev"
                description="action.navigate.prev.description"
                resource-bundle="messages.CodePinsBundle"
                icon="/icons/pin-prev.svg">
            <keyboard-shortcut keymap="$default" first-keystroke="alt shift LEFT"/>
            <add-to-group group-id="EditorActions" anchor="last"/>
        </action>

        <!-- Toggle CodePins tool window -->
        <action id="CodePins.TogglePinsToolWindowAction"
                class="cn.ilikexff.codepins.actions.TogglePinsToolWindowAction"
                text="action.toggle.window"
                description="action.toggle.window.description"
                resource-bundle="messages.CodePinsBundle"
                icon="/icons/pin-toggle.svg">
            <keyboard-shortcut keymap="$default" first-keystroke="alt shift T"/>
            <add-to-group group-id="ToolWindowsGroup" anchor="last"/>
        </action>

        <!-- Comment marker detector -->
        <action id="CodePins.PinCommentAction"
                class="cn.ilikexff.codepins.extensions.PinCommentAction"
                text="action.detect.markers"
                description="action.detect.markers.description"
                resource-bundle="messages.CodePinsBundle"
                icon="/icons/pin-comment.svg">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </action>
        
        <!-- Create CodePins action group -->
        <group id="CodePins.ActionGroup" text="action.group.name" description="action.group.description" resource-bundle="messages.CodePinsBundle" popup="true">
            <reference ref="CodePins.AddPinAction"/>
            <reference ref="CodePins.NavigateNextPinAction"/>
            <reference ref="CodePins.NavigatePrevPinAction"/>
            <reference ref="CodePins.TogglePinsToolWindowAction"/>
            <reference ref="CodePins.PinCommentAction"/>
            <add-to-group group-id="MainMenu" anchor="last"/>
        </group>
    </actions>
</idea-plugin>
