# Tooltip texts
tooltip.path=Path
tooltip.line=Line
tooltip.note=Note
tooltip.time=Created At
tooltip.author=Author

# UI texts - General
ui.app.name=CodePins
ui.app.description=Modern Code Bookmarks Solution
plugin.name=CodePins - Code Bookmarks

# UI texts - Buttons
button.add=Add
button.edit=Edit
button.delete=Delete
button.cancel=Cancel
button.ok=OK
button.apply=Apply
button.save=Save
button.close=Close
button.refresh=Refresh
button.import=Import
button.export=Export
button.clear=Clear All

# UI texts - Pin related
pin.add=Add CodePin Here
pin.remove=Remove CodePin
pin.edit=Edit CodePin
pin.block=Code Block Pin
pin.single=Single Line Pin
pin.added=Pin added at line {0}
pin.removed=Pin removed
pin.invalid=Invalid pin
pin.type.block=[Block]
pin.type.single=[Single]

# UI texts - Tags
tag.add=Add Tag
tag.edit=Edit Tags
tag.remove=Remove Tag
tag.filter=Filter by Tags
tag.clear=Clear Tags
tag.filter.panel=Tag Filter
tag.clear.filter=Clear Filter
tag.create=Create Tag
tag.create.custom=Add Custom Tag
tag.current.label=Current Tags:
tag.refresh=Refresh
tag.current=Current Tags
tag.empty=No Tags

# UI texts - Tag Editor Dialog
tag.editor.title=Edit Tags
tag.editor.instruction.title=Tag Usage Instructions
tag.editor.instruction.1=1. Enter tag name in the input field below, then press Enter or click the Add button
tag.editor.instruction.2=2. Select a tag in the list, then click the Delete button to remove it
tag.editor.instruction.3=3. Select a tag in the list, then click the Edit button or double-click the tag to edit it
tag.editor.instruction.4=4. No tag limit, the plugin is completely free and open source
tag.editor.current=Current Tags:
tag.editor.delete=Delete Selected Tag
tag.editor.add.new=Add New Tag:
tag.editor.add.placeholder=Enter tag name, press Enter or click Add button
tag.editor.add.button=Add
tag.editor.suggestions=Suggested Tags:

# UI texts - Import/Export
import.dialog.title=Import Pins
import.dialog.description=Import pins from a file
import.dialog.header=Import Pin Data
import.dialog.instruction=Select a file to import and set import options.
import.mode=Import Mode
import.mode.merge=Merge with existing pins
import.mode.replace=Replace all existing pins
import.file=Import File
import.file.select=Select File
import.file.selected=Selected file: {0}
import.file.none=No file selected
import.file.label=Import File
import.options.label=Import Options
import.warning=Warning:
import.warning.desc=Import operation may overwrite existing pins. Please ensure you have backed up important data.
import.file.chooser.title=Select Import File
import.file.chooser.desc=Select JSON file to import
import.success=Successfully imported {0} pins.
import.success.title=Import Successful

export.dialog.title=Export Pins
export.dialog.description=Export pins to a file
export.mode=Export Mode
export.mode.all=Export all pins
export.mode.selected=Export selected pins
export.file.select=Select Export Location
export.success=Successfully exported {0} pins to file:
{1}
export.success.title=Export Successful
export.description.html=You can choose to export all pins or only selected pins.<br>To select specific pins, choose the 'Export selected pins' option, then select the pins to export from the list below.
export.pins.list=Available Pins
export.no.pins=No pins to export. Please select at least one pin for export.
export.error=Export Error

# UI texts - Notes
note.edit=Edit Note
note.placeholder=Enter note here...
note.add.dialog.title=Add Pin
note.add.dialog.message=Enter pin note (optional):

# UI texts - Tooltip
tooltip.path=Path
tooltip.line=Line
tooltip.note=Note
tooltip.createdAt=Created At
tooltip.author=Author
tooltip.blockPin=ð Code Block Pin
tooltip.linePin=ð Line Pin
tooltip.searchPlaceholder=Search pins (note and path)

# UI texts - Settings
settings.title=CodePins Settings
settings.general=General Settings
settings.general.preview.height=Preview Window Height:
settings.general.confirm.delete=Confirm when deleting pins

settings.pin.add=Pin Addition Settings
settings.pin.add.show.note.dialog=Show note and tag dialog when adding pins via quick action
settings.pin.add.show.note.dialog.desc=When enabled, a dialog for entering notes and tags will be shown when adding pins using the text selection floating button

settings.comment=Comment Directive Settings
settings.comment.show.note.dialog=Show note and tag dialog when adding pins via comment directive
settings.comment.show.note.dialog.desc=When enabled, a dialog for entering notes and tags will be shown when adding pins using comment directives
settings.comment.auto.add.tag=Automatically add "Quick Add" tag
settings.comment.auto.add.tag.desc=When enabled, a "Quick Add" tag will be automatically added when adding pins using comment directives
settings.comment.use.completion.symbol=Use completion symbol
settings.comment.completion.symbol=Completion Symbol:
settings.comment.completion.symbol.desc=When enabled, pin addition will only be triggered when the completion symbol is entered after the comment directive, preventing premature triggering due to auto-save

settings.shortcuts=Keyboard Shortcuts
settings.shortcuts.info=CodePins provides the following default shortcuts:\n\n- Add Pin: Alt+Shift+P\n- Navigate to Next Pin: Alt+Shift+Right\n- Navigate to Previous Pin: Alt+Shift+Left\n- Toggle Pin Tool Window: Alt+Shift+T\n\nYou can customize these shortcuts in the IDE's 'Settings > Keymap'.\nSearch for "CodePins" to find all related actions.
settings.shortcuts.open=Open Keymap Settings

settings.support=Support Development
settings.support.free=â CodePins is now completely free and open source!
settings.support.thanks=Thank you for using CodePins! If this plugin has been helpful to you,
please consider supporting our development in the following ways:
settings.support.contribute=ð¤ Welcome to join open source contribution!
settings.support.contribute.desc=We sincerely invite you to maintain this open source project together:\nâ¢ ð Report bugs and suggest improvements\nâ¢ ð¡ Contribute new features and code optimizations\nâ¢ ð Improve documentation and user guides\nâ¢ ð Help translate to more languages\nâ¢ ð¢ Recommend CodePins to other developers
settings.support.github=â­ GitHub
settings.support.issue=ð Report Issue
settings.support.donate=â Buy Me a Coffee
settings.support.contribute.button=ð Contribute
settings.general=General Settings
settings.appearance=Appearance
settings.behavior=Behavior
settings.language=Language
settings.language.system=System Default
settings.language.english=English
settings.language.chinese=Chinese
settings.language.change.title=Language Changed
settings.language.change.message=Please restart IDE to apply the new language setting.

# UI texts - Dialogs
dialog.confirm.title=Confirmation
dialog.confirm.delete=Are you sure you want to delete this pin?
dialog.confirm.clear=Are you sure you want to clear all pins?

# UI texts - Notifications
notification.success=Success
notification.error=Error
notification.warning=Warning
notification.info=Information

# UI texts - Search
search.placeholder=Search pins...
search.no.results=No results found

# UI texts - Status
status.free=You are using CodePins Free Open Source Edition

# UI texts - Empty State
empty.title=No CodePins Added Yet
empty.description=CodePins help you mark important code locations and quickly return to them anytime.\n\nRight-click on a code line in the editor and select "Add CodePin Here" to add a pin.
empty.help.button=View Help

# UI texts - Context Menu
context.view.code=View Code Block
context.edit.note=Edit Note
context.edit.tags=Edit Tags
context.copy.pin=Copy Pin
context.delete.pin=Delete Pin
context.share.pin=Share Pin

# UI texts - Share Dialog
share.dialog.title=Share Pin
share.format=Share Format
share.format.markdown=Markdown
share.format.html=HTML
share.format.json=JSON
share.format.code.only=Code Only
share.format.image=Image
share.format.svg=SVG
share.method=Share Method
share.method.clipboard=Copy to Clipboard
share.method.file=Export to File
share.exclude.metadata=Exclude Metadata (not showing data)
share.show.line.numbers=Show Line Numbers
share.info=Will share {0} pin(s)
share.social=Social Share
share.watermark=Watermark Settings

# UI texts - Watermark Settings Dialog
watermark.dialog.title=Watermark Settings
watermark.type=Watermark Type
watermark.type.text=Text Watermark
watermark.type.none=No Watermark
watermark.text.settings=Text Watermark Settings
watermark.text=Watermark Text
watermark.position=Watermark Position
watermark.color=Watermark Color
watermark.opacity=Opacity
watermark.save.error=Failed to save watermark settings: {0}
watermark.error.title=Settings Error

# UI texts - Social Share Dialog
social.share.dialog.title=Share to Social Media
social.share.platform=Select Platform
social.share.platform.list=Share Platform
social.share.link.options=Link Options
social.share.link.expiration=Link Expiration
social.share.link.expiration.future=(Long-term valid, limitation features will be available in future versions)
social.share.password.protection=Password Protection
social.share.password.future=(Future feature)
social.share.info=Share Information
social.share.info.text=Will share {0} pin(s) to social media.\n\nNote: Shared links use GitHub Gist service for storage and are valid long-term.\nCustom expiration time and password protection features will be available in future versions.
social.share.error.select.platform=Please select a social media platform
social.share.error.title=Share Error
social.share.title=CodePins Share
social.share.title.single=CodePins Share: {0}
social.share.title.multiple=CodePins Share: {0} pins

# UI texts - Toolbar
toolbar.sort=Sort Pins
toolbar.sort.time.desc=By Creation Time (New â Old)
toolbar.sort.time.asc=By Creation Time (Old â New)
toolbar.sort.name.asc=By Name (A â Z)
toolbar.sort.name.desc=By Name (Z â A)
toolbar.sort.path.asc=By File Path (A â Z)
toolbar.sort.path.desc=By File Path (Z â A)
toolbar.share=Share Pins
toolbar.share.desc=Share selected pins
toolbar.delete.multiple=Delete Multiple
toolbar.delete.multiple.desc=Delete selected pins
toolbar.select.prompt=Please select pins first
toolbar.confirm.delete=Are you sure you want to delete {0} selected pins?
toolbar.confirm.delete.title=Confirm Deletion
toolbar.pin.count={0} pins

# UI texts - Comments
comment.marker.detected=Comment marker detected
comment.marker.processed=Comment marker processed
comment.detection.title=CodePins Comment Detection
comment.detection.scanning=Scanning file for comment markers...
comment.detection.completed=Comment marker detection completed
pin.exists.title=CodePins Pin Exists
pin.exists.range=A pin already exists in this range, not adding duplicate
pin.prepare.title=CodePins Preparing Pin
pin.created.title=CodePins Pin Created
pin.created.success=Pin successfully created: {0}
pin.confirm.note=Confirm or modify pin note:
pin.added.title=CodePins Pin Added
pin.added.from.comment=Automatically added pin from comment marker: {0}
pin.creating.title=CodePins Creating Pin
pin.creating.message=Creating pin...
pin.create.failed.title=CodePins Pin Creation Failed
pin.create.failed.message=Error creating pin: {0}
tag.quick.add=Quick Add
debug.file=File: {0}
debug.offset=Offset: {0}-{1}
debug.note=Note: {0}
debug.is.block=Is Block: {0}

# UI texts - Actions
action.add.pin=Add CodePin Here
action.add.pin.description=Pin current line for later reference
action.add.pin.cursor=Add Pin at Cursor
action.add.pin.cursor.description=Add a pin at the current cursor position
action.navigate.next=Navigate to Next Pin
action.navigate.next.description=Navigate to the next pin
action.navigate.prev=Navigate to Previous Pin
action.navigate.prev.description=Navigate to the previous pin
action.toggle.window=Toggle CodePins Tool Window
action.toggle.window.description=Show or hide the CodePins tool window
action.detect.markers=Detect Comment Markers
action.detect.markers.description=Detect @pin and @pin-block markers in comments
action.group.name=CodePins
action.group.description=CodePins actions
action.add.pin.title=Add CodePin
action.add.pin.note.prompt=Enter pin note (optional):
action.add.pin.failed.title=Add CodePin Failed
status.pin.added=â Pin added
status.pin.add.failed=â Pin add failed
pin.add.failed.retry=Failed to add pin, please try again later

# Tag Editor Dialog
tag.dialog.title=Edit Tags
tag.dialog.instruction=<html><b>Tag Usage Instructions:</b><br>1. Enter tag name in the input field below, then press Enter or click the Add button<br>2. Select a tag in the list, then click the Delete button to remove it<br>3. Select a tag in the list, then click the Edit button or double-click the tag to edit it<br>4. No tag limit, the plugin is completely free and open source</html>
tag.dialog.add=Add
tag.dialog.edit=Edit
tag.dialog.delete=Delete
tag.dialog.existing=Existing Tags:
tag.dialog.new=New Tag:
tag.dialog.input.placeholder=Enter tag name here...
tag.dialog.exists=Tag '{0}' already exists
tag.dialog.exists.title=Duplicate Tag
tag.dialog.empty=No existing tags
tag.dialog.all=All tags have been added
tag.dialog.current=Current tags (click to add):