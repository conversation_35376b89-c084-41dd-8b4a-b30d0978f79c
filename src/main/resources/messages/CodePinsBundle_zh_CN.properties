# å·¥å·æç¤ºææ¬
tooltip.path=è·¯å¾
tooltip.line=è¡å·
tooltip.note=å¤æ³¨
tooltip.time=åå»ºæ¶é´
tooltip.author=åå»ºè

# UI ææ¬ - éç¨
ui.app.name=CodePins
ui.app.description=ç°ä»£ä»£ç ä¹¦ç­¾è§£å³æ¹æ¡
plugin.name=CodePins - ä»£ç å¾é

# UI ææ¬ - æé®
button.add=æ·»å 
button.edit=ç¼è¾
button.delete=å é¤
button.cancel=åæ¶
button.ok=ç¡®å®
button.apply=åºç¨
button.save=ä¿å­
button.close=å³é­
button.refresh=å·æ°
button.import=å¯¼å¥
button.export=å¯¼åº
button.clear=æ¸é¤å¨é¨

# UI ææ¬ - å¾éç¸å³
pin.add=æ·»å å¾é
pin.remove=ç§»é¤å¾é
pin.edit=ç¼è¾å¾é
pin.block=ä»£ç åå¾é
pin.single=åè¡å¾é
pin.added=å·²æ·»å å¾éå°ç¬¬ {0} è¡
pin.removed=å¾éå·²ç§»é¤
pin.invalid=æ æå¾é
pin.type.block=[ä»£ç å]
pin.type.single=[åè¡]

# UI ææ¬ - æ ç­¾
tag.add=æ·»å æ ç­¾
tag.edit=ç¼è¾æ ç­¾
tag.remove=ç§»é¤æ ç­¾
tag.filter=ææ ç­¾ç­é
tag.clear=æ¸é¤æ ç­¾
tag.filter.panel=æ ç­¾ç­é
tag.clear.filter=æ¸é¤ç­é
tag.create=æ°å»ºæ ç­¾
tag.create.custom=æ·»å èªå®ä¹æ ç­¾
tag.current.label=å½åæ ç­¾ï¼
tag.refresh=å·æ°
tag.current=å½åæ ç­¾
tag.empty=ææ æ ç­¾

# UI ææ¬ - æ ç­¾ç¼è¾å¯¹è¯æ¡
tag.editor.title=ç¼è¾æ ç­¾
tag.editor.instruction.title=æ ç­¾ä½¿ç¨è¯´æ
tag.editor.instruction.1=1. å¨ä¸æ¹è¾å¥æ¡ä¸­è¾å¥æ ç­¾åç§°ï¼ç¶åç¹å»âæ·»å âæé®
tag.editor.instruction.2=2. éæ©åè¡¨ä¸­çæ ç­¾ï¼ç¶åç¹å»âå é¤âæé®å é¤è¯¥æ ç­¾
tag.editor.instruction.3=3. éæ©åè¡¨ä¸­çæ ç­¾ï¼ç¶åç¹å»âç¼è¾âæé®æåå»æ ç­¾è¿è¡ç¼è¾
tag.editor.instruction.4=4. æ æ ç­¾éå¶ï¼æä»¶å®å¨åè´¹å¹¶å¼æº
tag.editor.current=å½åæ ç­¾:
tag.editor.delete=å é¤éä¸­æ ç­¾
tag.editor.add.new=æ·»å æ°æ ç­¾:
tag.editor.add.placeholder=è¾å¥æ ç­¾åç§°ï¼åè½¦æç¹å»æ·»å æé®
tag.editor.add.button=æ·»å 
tag.editor.suggestions=å»ºè®®æ ç­¾:

# UI ææ¬ - å¯¼å¥/å¯¼åº
import.dialog.title=å¯¼å¥å¾é
import.dialog.description=ä»æä»¶å¯¼å¥å¾é
import.dialog.header=å¯¼å¥å¾éæ°æ®
import.dialog.instruction=éæ©è¦å¯¼å¥çæä»¶ï¼å¹¶è®¾ç½®å¯¼å¥éé¡¹ã
import.mode=å¯¼å¥æ¨¡å¼
import.mode.merge=ä¸ç°æå¾éåå¹¶
import.mode.replace=æ¿æ¢ææç°æå¾é
import.file=å¯¼å¥æä»¶
import.file.select=éæ©æä»¶
import.file.selected=å·²éæ©æä»¶ï¼{0}
import.file.none=æªéæ©æä»¶
import.file.label=å¯¼å¥æä»¶
import.options.label=å¯¼å¥éé¡¹
import.warning=æ³¨æï¼
import.warning.desc=å¯¼å¥æä½å¯è½ä¼è¦çç°æå¾éãè¯·ç¡®ä¿æ¨å·²å¤ä»½éè¦æ°æ®ã
import.file.chooser.title=éæ©å¯¼å¥æä»¶
import.file.chooser.desc=éæ©è¦å¯¼å¥ç JSON æä»¶
import.success=æåå¯¼å¥ {0} ä¸ªå¾éã
import.success.title=å¯¼å¥æå

export.dialog.title=å¯¼åºå¾é
export.dialog.description=å°å¾éå¯¼åºå°æä»¶
export.mode=å¯¼åºæ¨¡å¼
export.mode.all=å¯¼åºææå¾é
export.mode.selected=å¯¼åºéä¸­å¾é
export.file.select=éæ©å¯¼åºä½ç½®
export.success=æåå¯¼åº {0} ä¸ªå¾éå°æä»¶ï¼
{1}
export.success.title=å¯¼åºæå
export.description.html=æ¨å¯ä»¥éæ©å¯¼åºå¨é¨å¾éæä»å¯¼åºéä¸­çå¾éã<br>å¦ééæ©ç¹å®å¾éï¼è¯·éæ©'ä»å¯¼åºéä¸­çå¾é'éé¡¹ï¼ç¶åå¨ä¸æ¹åè¡¨ä¸­éæ©è¦å¯¼åºçå¾éã
export.pins.list=å¯ç¨å¾é
export.no.pins=æ²¡æå¾éå¯å¯¼åºãè¯·éæ©è³å°ä¸ä¸ªå¾éè¿è¡å¯¼åºã
export.error=å¯¼åºéè¯¯

# UI ææ¬ - éç¥
notification.copy.success=å¾éå¤å¶æåï¼
notification.copy.success.title=å¤å¶æå
notification.restore.success=å¾éæ¢å¤æåï¼
notification.restore.success.title=æ¢å¤æå
notification.delete.success=å¾éå é¤æåï¼
notification.delete.success.title=å é¤æå
notification.clear.success=ææå¾éæ¸é¤æåï¼
notification.clear.success.title=æ¸é¤æå

# UI ææ¬ - å¤æ³¨
note.edit=ç¼è¾å¤æ³¨
note.placeholder=å¨æ­¤è¾å¥å¤æ³¨...
note.add.dialog.title=æ·»å å¾é
note.add.dialog.message=è¯·è¾å¥å¾éå¤æ³¨ï¼å¯éï¼ï¼

# UI ææ¬ - æ¬æµ®çª
tooltip.path=è·¯å¾
tooltip.line=è¡å·
tooltip.note=å¤æ³¨
tooltip.createdAt=åå»ºäº
tooltip.author=ä½è
tooltip.blockPin=ð ä»£ç åå¾é
tooltip.linePin=ð åè¡å¾é
tooltip.searchPlaceholder=æç´¢å¾éï¼æ¯æå¤æ³¨ä¸è·¯å¾ï¼

# UI ææ¬ - è®¾ç½®
settings.title=CodePins è®¾ç½®
settings.general=å¸¸è§è®¾ç½®
settings.general.preview.height=é¢è§çªå£é«åº¦:
settings.general.confirm.delete=å é¤å¾éæ¶ç¡®è®¤

settings.pin.add=å¾éæ·»å è®¾ç½®
settings.pin.add.show.note.dialog=å¿«æ·æ·»å å¾éæ¶æ¾ç¤ºå¤æ³¨åæ ç­¾å¯¹è¯æ¡
settings.pin.add.show.note.dialog.desc=å¼å¯åï¼ä½¿ç¨éæ©ææ¬æµ®å¨æé®æ·»å å¾éæ¶ï¼å°æ¾ç¤ºå¤æ³¨åæ ç­¾å¯¹è¯æ¡

settings.comment=æ³¨éæä»¤è®¾ç½®
settings.comment.show.note.dialog=æ³¨éæä»¤æ·»å å¾éæ¶æ¾ç¤ºå¤æ³¨åæ ç­¾å¯¹è¯æ¡
settings.comment.show.note.dialog.desc=å¼å¯åï¼ä½¿ç¨æ³¨éæä»¤æ·»å å¾éæ¶ï¼å°æ¾ç¤ºå¤æ³¨åæ ç­¾å¯¹è¯æ¡
settings.comment.auto.add.tag=èªå¨æ·»å âå¿«æ·æ·»å âæ ç­¾
settings.comment.auto.add.tag.desc=å¼å¯åï¼ä½¿ç¨æ³¨éæä»¤æ·»å å¾éæ¶ï¼èªå¨æ·»å âå¿«æ·æ·»å âæ ç­¾
settings.comment.use.completion.symbol=ä½¿ç¨å®ææä»¤ç¬¦å·
settings.comment.completion.symbol=å®ææä»¤ç¬¦å·:
settings.comment.completion.symbol.desc=å¼å¯åï¼åªæå¨æ³¨éæä»¤åè¾å¥å®æç¬¦å·æ¶æä¼è§¦åå¾éæ·»å ï¼é¿åèªå¨ä¿å­å¯¼è´è¿æ©è§¦å

settings.shortcuts=å¿«æ·é®è®¾ç½®
settings.shortcuts.info=CodePins æä¾ä»¥ä¸é»è®¤å¿«æ·é®ï¼\n\n- æ·»å å¾é: Alt+Shift+P\n- å¯¼èªå°ä¸ä¸ä¸ªå¾é: Alt+Shift+Right\n- å¯¼èªå°ä¸ä¸ä¸ªå¾é: Alt+Shift+Left\n- åæ¢å¾éå·¥å·çªå£: Alt+Shift+T\n\næ¨å¯ä»¥å¨ IDE ç'è®¾ç½® > é®çå¿«æ·é®'ä¸­èªå®ä¹è¿äºå¿«æ·é®ã\næç´¢ "CodePins" ä»¥æ¾å°ææç¸å³æä½ã
settings.shortcuts.open=æå¼é®çå¿«æ·é®è®¾ç½®

settings.support=æ¯æå¼å
settings.support.free=â CodePins ç°å¨å®å¨åè´¹å¼æºï¼
settings.support.thanks=æè°¢æ¨ä½¿ç¨ CodePinsï¼å¦æè¿ä¸ªæä»¶å¯¹æ¨æå¸®å©ï¼
è¯·èèéè¿ä»¥ä¸æ¹å¼æ¯ææä»¬çå¼åï¼
settings.support.contribute=ð¤ æ¬¢è¿å å¥å¼æºè´¡ç®ï¼
settings.support.contribute.desc=æä»¬è¯æéè¯·æ¨ä¸èµ·ç»´æ¤è¿ä¸ªå¼æºé¡¹ç®ï¼\nâ¢ ð æ¥å Bug åæåºæ¹è¿å»ºè®®\nâ¢ ð¡ è´¡ç®æ°åè½åä»£ç ä¼å\nâ¢ ð å®åææ¡£åä½¿ç¨æå\nâ¢ ð å¸®å©ç¿»è¯å°æ´å¤è¯­è¨\nâ¢ ð¢ åå¶ä»å¼åèæ¨è CodePins
settings.support.github=â­ GitHub
settings.support.issue=ð æ¥åé®é¢
settings.support.donate=â è¯·æååå¡
settings.support.contribute.button=ð åä¸è´¡ç®
settings.general=éç¨è®¾ç½®
settings.appearance=å¤è§
settings.behavior=è¡ä¸º
settings.language=è¯­è¨
settings.language.system=ç³»ç»é»è®¤
settings.language.english=è±æ
settings.language.chinese=ä¸­æ
settings.language.change.title=è¯­è¨å·²æ´æ¹
settings.language.change.message=è¯·éå¯ IDE ä»¥åºç¨æ°çè¯­è¨è®¾ç½®ã

# UI ææ¬ - å¯¹è¯æ¡
dialog.confirm.title=ç¡®è®¤
dialog.confirm.delete=æ¨ç¡®å®è¦å é¤è¿ä¸ªå¾éåï¼
dialog.confirm.clear=æ¨ç¡®å®è¦æ¸é¤ææå¾éåï¼

# UI ææ¬ - éç¥
notification.success=æå
notification.error=éè¯¯
notification.warning=è­¦å
notification.info=ä¿¡æ¯

# UI ææ¬ - æç´¢
search.placeholder=æç´¢å¾é...
search.no.results=æªæ¾å°ç»æ

# UI ææ¬ - ç¶æ
status.free=æ¨æ­£å¨ä½¿ç¨ CodePins åè´¹å¼æºç

# UI ææ¬ - ç©ºç¶æ
empty.title=è¿æ²¡ææ·»å ä»»ä½å¾é
empty.description=å¾éå¯ä»¥å¸®å©æ¨æ è®°éè¦çä»£ç ä½ç½®ï¼\nå¹¶éæ¶å¿«éè¿åæ¥çã\n\nå¨ç¼è¾å¨ä¸­å³é®ç¹å»ä»£ç è¡ï¼\néæ©"æ·»å å¾é"æ·»å å¾éã
empty.help.button=æ¥çå¸®å©

# UI ææ¬ - å³é®èå
context.view.code=æ¥çä»£ç å
context.edit.note=ä¿®æ¹å¤æ³¨
context.edit.tags=ç¼è¾æ ç­¾
context.copy.pin=å¤å¶å¾é
context.delete.pin=å é¤å¾é
context.share.pin=åäº«å¾é

# UI ææ¬ - åäº«å¯¹è¯æ¡
share.dialog.title=åäº«å¾é
share.format=åäº«æ ¼å¼
share.format.markdown=Markdown
share.format.html=HTML
share.format.json=JSON
share.format.code.only=ä»ä»£ç 
share.format.image=å¾ç
share.format.svg=SVG
share.method=åäº«æ¹å¼
share.method.clipboard=å¤å¶å°åªè´´æ¿
share.method.file=å¯¼åºå°æä»¶
share.exclude.metadata=ä»åäº«ä»£ç åå®¹ï¼ä¸å«åæ°æ®ï¼
share.show.line.numbers=æ¾ç¤ºè¡å·
share.info=å°åäº« {0} ä¸ªå¾é
share.social=ç¤¾äº¤åäº«
share.watermark=æ°´å°è®¾ç½®

# UI ææ¬ - æ°´å°è®¾ç½®å¯¹è¯æ¡
watermark.dialog.title=æ°´å°è®¾ç½®
watermark.type=æ°´å°ç±»å
watermark.type.text=ææ¬æ°´å°
watermark.type.none=æ æ°´å°
watermark.text.settings=ææ¬æ°´å°è®¾ç½®
watermark.text=æ°´å°ææ¬
watermark.position=æ°´å°ä½ç½®
watermark.color=æ°´å°é¢è²
watermark.opacity=éæåº¦
watermark.save.error=ä¿å­æ°´å°è®¾ç½®å¤±è´¥: {0}
watermark.error.title=è®¾ç½®éè¯¯

# UI ææ¬ - ç¤¾äº¤åäº«å¯¹è¯æ¡
social.share.dialog.title=åäº«å°ç¤¾äº¤åªä½
social.share.platform=éæ©å¹³å°
social.share.platform.list=åäº«å¹³å°
social.share.link.options=é¾æ¥éé¡¹
social.share.link.expiration=é¾æ¥æææ
social.share.link.expiration.future=(é¿æææï¼éå¶åè½å°å¨æªæ¥çæ¬ä¸­æä¾)
social.share.password.protection=å¯ç ä¿æ¤
social.share.password.future=(æªæ¥åè½)
social.share.info=åäº«ä¿¡æ¯
social.share.info.text=å°åäº« {0} ä¸ªå¾éå°ç¤¾äº¤åªä½ã\n\næ³¨æï¼åäº«é¾æ¥ä½¿ç¨GitHub Gistæå¡å­å¨ï¼é¾æ¥é¿æææã\nèªå®ä¹è¿ææ¶é´åå¯ç ä¿æ¤åè½å°å¨æªæ¥çæ¬ä¸­æä¾ã
social.share.error.select.platform=è¯·éæ©ä¸ä¸ªç¤¾äº¤åªä½å¹³å°
social.share.error.title=åäº«éè¯¯
social.share.title=CodePinsåäº«
social.share.title.single=CodePinsåäº«: {0}
social.share.title.multiple=CodePinsåäº«: {0}ä¸ªå¾é

# UI ææ¬ - å·¥å·æ 
toolbar.sort=æåºå¾é
toolbar.sort.time.desc=æåå»ºæ¶é´ï¼æ°âæ§ï¼
toolbar.sort.time.asc=æåå»ºæ¶é´ï¼æ§âæ°ï¼
toolbar.sort.name.asc=æåç§°ï¼AâZï¼
toolbar.sort.name.desc=æåç§°ï¼ZâAï¼
toolbar.sort.path.asc=ææä»¶è·¯å¾ï¼AâZï¼
toolbar.sort.path.desc=ææä»¶è·¯å¾ï¼ZâAï¼
toolbar.share=åäº«å¾é
toolbar.share.desc=åäº«éä¸­çå¾é
toolbar.delete.multiple=æ¹éå é¤
toolbar.delete.multiple.desc=å é¤éä¸­çå¾é
toolbar.select.prompt=è¯·åéæ©è¦æä½çå¾é
toolbar.confirm.delete=ç¡®å®è¦å é¤éä¸­ç {0} ä¸ªå¾éåï¼
toolbar.confirm.delete.title=ç¡®è®¤å é¤
toolbar.pin.count={0} ä¸ªå¾é

# UI ææ¬ - æ³¨é
comment.marker.detected=æ£æµå°æ³¨éæ è®°
comment.marker.processed=æ³¨éæ è®°å·²å¤ç
comment.detection.title=CodePins æ³¨éæ£æµ
comment.detection.scanning=æ­£å¨æ£æµæä»¶ä¸­çæ³¨éæ è®°...
comment.detection.completed=æ³¨éæ è®°æ£æµå®æ
pin.exists.title=CodePins å¾éå·²å­å¨
pin.exists.range=è¯¥èå´å·²æå¾éï¼ä¸éå¤æ·»å 
pin.prepare.title=CodePins åå¤åå»ºå¾é
pin.created.title=CodePins å¾éåå»ºæå
pin.created.success=å¾éå·²æååå»º: {0}
pin.confirm.note=è¯·ç¡®è®¤æä¿®æ¹å¾éå¤æ³¨ï¼
pin.added.title=CodePins å¾éå·²æ·»å 
pin.added.from.comment=æ ¹æ®æ³¨éæä»¤èªå¨æ·»å äºå¾é: {0}
pin.creating.title=CodePins æ­£å¨åå»ºå¾é
pin.creating.message=æ­£å¨åå»ºå¾é...
pin.create.failed.title=CodePins å¾éåå»ºå¤±è´¥
pin.create.failed.message=åå»ºå¾éæ¶åçéè¯¯: {0}
tag.quick.add=å¿«æ·æ·»å 
debug.file=æä»¶: {0}
debug.offset=åç§»é: {0}-{1}
debug.note=å¤æ³¨: {0}
debug.is.block=æ¯å¦ä»£ç å: {0}

# UI ææ¬ - æä½
action.add.pin=æ·»å å¾é
action.add.pin.description=å°å½åè¡æ·»å ä¸ºå¾é
action.add.pin.cursor=å¨åæ å¤æ·»å å¾é
action.add.pin.cursor.description=å¨å½ååæ ä½ç½®æ·»å å¾é
action.navigate.next=å¯¼èªå°ä¸ä¸ä¸ªå¾é
action.navigate.next.description=å¯¼èªå°ä¸ä¸ä¸ªå¾éä½ç½®
action.navigate.prev=å¯¼èªå°ä¸ä¸ä¸ªå¾é
action.navigate.prev.description=å¯¼èªå°ä¸ä¸ä¸ªå¾éä½ç½®
action.toggle.window=åæ¢å¾éå·¥å·çªå£
action.toggle.window.description=æ¾ç¤ºæéèå¾éå·¥å·çªå£
action.detect.markers=æ£æµæ³¨éæ è®°
action.detect.markers.description=æ£æµæ³¨éä¸­ç @pin å @pin-block æ è®°
action.group.name=CodePins
action.group.description=CodePins æä½
action.add.pin.title=æ·»å å¾é
action.add.pin.note.prompt=è¯·è¾å¥å¾éå¤æ³¨ï¼å¯éï¼ï¼
action.add.pin.failed.title=æ·»å å¾éå¤±è´¥
status.pin.added=â å¾éå·²æ·»å 
status.pin.add.failed=â å¾éæ·»å å¤±è´¥
pin.add.failed.retry=æ·»å å¾éå¤±è´¥ï¼è¯·ç¨åéè¯

# æ ç­¾ç¼è¾å¯¹è¯æ¡
tag.dialog.title=ç¼è¾æ ç­¾
tag.dialog.instruction=<html><b>æ ç­¾ä½¿ç¨è¯´æï¼</b><br>1. å¨ä¸æ¹è¾å¥æ¡ä¸­è¾å¥æ ç­¾åç§°ï¼ç¶åæåè½¦æç¹å»æ·»å æé®<br>2. éä¸­åè¡¨ä¸­çæ ç­¾ï¼ç¶åç¹å»å é¤æé®å¯å é¤æ ç­¾<br>3. éä¸­åè¡¨ä¸­çæ ç­¾ï¼ç¶åç¹å»ç¼è¾æé®æåå»æ ç­¾å¯ç¼è¾æ ç­¾<br>4. æ ç­¾æ°éæ éå¶ï¼æä»¶å·²å®å¨åè´¹å¼æº</html>
tag.dialog.add=æ·»å 
tag.dialog.edit=ç¼è¾
tag.dialog.delete=å é¤
tag.dialog.existing=ç°ææ ç­¾ï¼
tag.dialog.new=æ°æ ç­¾ï¼
tag.dialog.input.placeholder=å¨æ­¤è¾å¥æ ç­¾åç§°...
tag.dialog.exists=æ ç­¾ '{0}' å·²å­å¨
tag.dialog.exists.title=æ ç­¾éå¤
tag.dialog.empty=ææ å·²ææ ç­¾
tag.dialog.all=æææ ç­¾å·²æ·»å 
tag.dialog.current=å½åå·²ææ ç­¾ï¼ç¹å»æ·»å ï¼ï¼