name: CodePins CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
          cache: gradle

      - name: <PERSON> execute permission to Gradle
        run: chmod +x ./gradlew

      - name: Build plugin
        run: ./gradlew build

      - name: Run Plugin Verifier
        run: ./gradlew verifyPlugin

      - name: Upload Plugin Artifact
        uses: actions/upload-artifact@v4
        with:
          name: codepins-plugin
          path: build/libs/*.jar
          retention-days: 7

      - name: Upload Test Reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-reports
          path: build/reports/
          retention-days: 7
          
  test-matrix:
    runs-on: ${{ matrix.os }}
    timeout-minutes: 30
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
          cache: gradle
      
      - name: <PERSON> execute permission to Gradle
        run: chmod +x ./gradlew
        shell: bash
      
      - name: Run tests
        run: ./gradlew test
        
      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.os }}
          path: build/test-results/
          retention-days: 7
